import { useState, useCallback } from 'react';
import { postRequest } from '../utils/apiHandler';
import { ComponentType } from '../components/Dashboard/Views Section/types';

export interface FilterPayload {
  fileId: string;
  fileName: string;
  selectedColumns: {
    indices: number[];
    headers: string[];
  };
  dateFilter: {
    startDate: string | null;
    endDate: string | null;
  };
  conditionalFilters: any[];
  annotationFilters: any[];
  operationFilters: any[];
}

export interface PanelLoadingStates {
  [ComponentType.TimeSeriesPanel]: boolean;
  [ComponentType.OverviewPanel]: boolean;
  [ComponentType.HistogramPanel]: boolean;
  [ComponentType.DataTablePanel]: boolean;
  [ComponentType.ScatterPlotPanel]: boolean;
  [ComponentType.XbarRbarPanel]: boolean;
}

export interface PanelErrorStates {
  [ComponentType.TimeSeriesPanel]: string | null;
  [ComponentType.OverviewPanel]: string | null;
  [ComponentType.HistogramPanel]: string | null;
  [ComponentType.DataTablePanel]: string | null;
  [ComponentType.ScatterPlotPanel]: string | null;
  [ComponentType.XbarRbarPanel]: string | null;
}

export const useFetchFilteredFileData = () => {
  const [panelLoadingStates, setPanelLoadingStates] = useState<PanelLoadingStates>({
    [ComponentType.TimeSeriesPanel]: false,
    [ComponentType.OverviewPanel]: false,
    [ComponentType.HistogramPanel]: false,
    [ComponentType.DataTablePanel]: false,
    [ComponentType.ScatterPlotPanel]: false,
    [ComponentType.XbarRbarPanel]: false,
  });

  const [panelErrorStates, setPanelErrorStates] = useState<PanelErrorStates>({
    [ComponentType.TimeSeriesPanel]: null,
    [ComponentType.OverviewPanel]: null,
    [ComponentType.HistogramPanel]: null,
    [ComponentType.DataTablePanel]: null,
    [ComponentType.ScatterPlotPanel]: null,
    [ComponentType.XbarRbarPanel]: null,
  });

  // Memoized function to fetch data for all active panels
  const fetchAllPanelData = useCallback(async (
    panelTypes: ComponentType[],
    filterPayload: FilterPayload,
    onPanelDataReceived: (panelType: ComponentType, data: any) => void
  ) => {
    // Use Promise.all instead of forEach to properly handle async operations
    const promises = panelTypes.map(async (panelType) => {
      try {
        // Set loading state
        setPanelLoadingStates(prev => ({
          ...prev,
          [panelType]: true
        }));

        // Clear any previous errors
        setPanelErrorStates(prev => ({
          ...prev,
          [panelType]: null
        }));

        // Use existing file endpoint with panel type as query parameter
        const endpoint = `/file/explore-file/${filterPayload.fileId}?panelType=${panelType}`;


        // Call POST API with payload in body and panelType in query
        const response = await postRequest(endpoint, filterPayload);

        if (response.data && response.data.status === 200) {
          onPanelDataReceived(panelType, response.data.data);
        } else {
          throw new Error(response.data?.message || 'Failed to fetch panel data');
        }
      } catch (error: any) {
        console.error(`Error fetching data for ${panelType}:`, error);

        // Set error state
        setPanelErrorStates(prev => ({
          ...prev,
          [panelType]: error.message || 'Failed to fetch data'
        }));
      } finally {
        console.log(`Clearing loading state for ${panelType}`);
        // Clear loading state
        setPanelLoadingStates(prev => ({
          ...prev,
          [panelType]: false
        }));
      }
    });

    // Wait for all API calls to complete
    await Promise.all(promises);
  }, []);

  // Memoized function to create filter payload from current state
  const createFilterPayload = useCallback((
    fileId: string,
    fileName: string,
    selectedColumns: any,
    dateFilter: any,
    conditionalFilters: any[],
    annotationFilters: any[],
    operationFilters: any[]
  ): FilterPayload => {
    return {
      fileId,
      fileName,
      selectedColumns,
      dateFilter,
      conditionalFilters,
      annotationFilters,
      operationFilters
    };
  }, []);

  return {
    fetchAllPanelData,
    createFilterPayload,
    panelLoadingStates,
    panelErrorStates
  };
};
