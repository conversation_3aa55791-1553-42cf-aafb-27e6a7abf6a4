import { Router } from "express";
const router =Router()

// ********Import the controllers ******

import { upload } from "../middlewares/multer.middleware.js";
import { uploadFile ,getFile,getFileList,getFileColumns, removeFile ,mapCsvFields,csvMappingHistory,updateFile , createClusterAllRunCSVFile, getReportData, uploadJsonFile, getJsonFileData, updateJsonFile, deleteJsonFile, exploreFileData} from "../controllers/files.controllers.js";
import {authenticateToken} from '../middlewares/jwt.js'
import multer from "multer";
const uploadMiddleware = multer({ dest: "uploads/" });

// ********Define path of controllers ******
router.route('/columns/:csvId').get(getFileColumns)
router.route('/map-csv/:csvId').put(authenticateToken,mapCsvFields)
router.route('/csv-mapping-history').post(authenticateToken,csvMappingHistory)
router.route('/upload').post(uploadMiddleware.single("file"), authenticateToken, uploadFile)
router.route('/update').post(authenticateToken, upload.single('file'), updateFile);
router.route('/upload-json-file').post( authenticateToken, uploadJsonFile)
router.route('/get-json-file').post( authenticateToken, getJsonFileData)
router.route('/update-json-file').post(authenticateToken, updateJsonFile);
router.route('/delete-json-file/:filename').delete(authenticateToken, deleteJsonFile);

router.route('/:csvId').get(authenticateToken, getFile)
router.route('/explore-file/:csvId').post(authenticateToken, exploreFileData)
router.route('/:csv_id').delete(authenticateToken, removeFile)
router.route('/').get(authenticateToken , getFileList)
router.route('/cluster-all-run/create-&-upload').post(authenticateToken,createClusterAllRunCSVFile)
router.route('/report-data').post(authenticateToken,getReportData)


export default router